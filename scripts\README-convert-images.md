# Script Optimizado de Conversión de Imágenes WebP

## Descripción

Este es el script estándar del proyecto para la conversión y gestión de imágenes. Ha sido optimizado para ser el más eficiente, robusto y fácil de usar entre todos los scripts disponibles.

## Características Principales

### ✅ **Funcionalidades Avanzadas**
- **Conversión automática** de JPG, JPEG, PNG y JFIF a formato WebP
- **Optimización inteligente** de imágenes WebP existentes
- **Nomenclatura estandarizada** siguiendo el patrón `[nombre-atraccion]-[número].webp`
- **Actualización automática** del catálogo de imágenes (`image-catalog.json`)
- **Validación robusta** de rutas y manejo de errores
- **Estadísticas detalladas** de procesamiento y ahorro de espacio

### 🚀 **Rendimiento Superior**
- Utiliza **Sharp** (biblioteca más rápida para procesamiento de imágenes)
- Procesamiento por lotes optimizado
- Calidad WebP configurable (75-85% recomendado)
- Verificación inteligente para evitar re-procesar archivos

### 🌐 **Compatibilidad Multiplataforma**
- ✅ Windows
- ✅ macOS  
- ✅ Linux

### 🛡️ **Manejo Robusto de Errores**
- Validación de directorios antes del procesamiento
- Manejo individual de errores por archivo
- Limpieza automática de archivos temporales
- Mensajes de error descriptivos con sugerencias

## Uso

### Ejecución Básica
```bash
node scripts/convert-images.js
```

### Desde el directorio raíz del proyecto
```bash
npm run convert-images
```

## Configuración

### Parámetros Principales
```javascript
const WEBP_QUALITY = 80; // Calidad WebP (75-85% recomendado)
const STANDARD_DIMENSIONS = {
  width: 800,
  height: 600
};
```

### Atracciones Soportadas
El script procesa automáticamente las siguientes carpetas:
- `cocha-resbaladero`
- `iglesia-san-andres`
- `parque-dinosaurios`
- `pueblo-de-pica`
- `reserva-pampa-del-tamarugal`
- `salar-del-huasco`

## Estructura de Archivos

### Entrada
```
client/src/assets/atracciones/
├── cocha-resbaladero/
│   ├── imagen1.jpg
│   └── imagen2.png
└── parque-dinosaurios/
    ├── foto1.jpeg
    └── foto2.jfif
```

### Salida
```
client/src/assets/atracciones/
├── cocha-resbaladero/
│   ├── cocha-resbaladero-1.webp
│   └── cocha-resbaladero-2.webp
└── parque-dinosaurios/
    ├── parque-dinosaurios-1.webp
    └── parque-dinosaurios-2.webp
```

## Actualización del Catálogo

El script actualiza automáticamente `client/src/assets/image-catalog.json` con:
- Rutas correctas de las imágenes procesadas
- Metadatos estándar (dimensiones, formato, texto alternativo)
- Información de atracciones (título, descripción, distancia)

### Ejemplo de entrada en el catálogo:
```json
{
  "id": "cocha-resbaladero",
  "title": "Cocha Resbaladero",
  "description": "Reconocidas piscinas naturales...",
  "distance": "A 10 minutos",
  "images": [
    {
      "path": "src/assets/atracciones/cocha-resbaladero/cocha-resbaladero-1.webp",
      "alt": "Cocha Resbaladero - Vista principal",
      "width": 800,
      "height": 600,
      "format": "webp"
    }
  ]
}
```

## Dependencias

### Requeridas
- **sharp**: Procesamiento de imágenes (ya instalado como devDependency)
- **fs**: Sistema de archivos (nativo de Node.js)
- **path**: Manejo de rutas (nativo de Node.js)

### Instalación
```bash
npm install
```

## Ventajas sobre Otros Scripts

| Característica | convert-images.js | Otros Scripts |
|----------------|-------------------|---------------|
| **Velocidad** | ⚡ Sharp (más rápido) | 🐌 Imagemin/PowerShell |
| **Catálogo automático** | ✅ Sí | ❌ No |
| **Validación robusta** | ✅ Sí | ⚠️ Básica |
| **Manejo de errores** | ✅ Avanzado | ⚠️ Básico |
| **Multiplataforma** | ✅ Sí | ⚠️ Limitado |
| **Nomenclatura estándar** | ✅ Automática | ⚠️ Manual |
| **Optimización WebP** | ✅ Inteligente | ❌ No |

## Solución de Problemas

### Error: "El directorio de assets no existe"
```bash
# Verificar que estás en el directorio raíz del proyecto
pwd
# Debe mostrar: .../Posada2.0

# Verificar estructura de directorios
ls -la client/src/assets/atracciones/
```

### Error: "No se pudieron validar los directorios"
```bash
# Crear directorios faltantes
mkdir -p client/src/assets/atracciones
mkdir -p client/src/assets
```

### Error: "Permission denied"
```bash
# En Linux/macOS, verificar permisos
chmod -R 755 client/src/assets/
```

## Recomendación Final

**Este script (`scripts/convert-images.js`) es el estándar oficial del proyecto** para la conversión de imágenes. Ofrece la mejor combinación de rendimiento, funcionalidad y facilidad de uso.

### Para nuevas imágenes:
1. Colocar archivos en la carpeta correspondiente en `client/src/assets/atracciones/`
2. Ejecutar `node scripts/convert-images.js`
3. El script se encargará de todo automáticamente

### Mantenimiento:
- Ejecutar periódicamente para optimizar imágenes existentes
- El script es seguro para ejecutar múltiples veces
- No re-procesa archivos que ya están optimizados
