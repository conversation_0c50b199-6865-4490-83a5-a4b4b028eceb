import React from 'react';
import { motion } from 'framer-motion';
import ImageManager from './ImageManager';
import imageCatalog from '../../assets/image-catalog.json';

interface AttractionCardEnhancedProps {
  attractionId: string;
  index: number;
}

/**
 * Componente mejorado para mostrar tarjetas de atracciones turísticas
 * Utiliza el sistema de gestión de activos para cargar imágenes y datos
 *
 * @param attractionId - ID de la atracción en el catálogo
 * @param index - Índice para la animación escalonada
 */
const AttractionCardEnhanced: React.FC<AttractionCardEnhancedProps> = ({
  attractionId,
  index
}) => {
  // Buscar la atracción en el catálogo
  const attraction = imageCatalog.attractions.find(attr => attr.id === attractionId);

  if (!attraction) {
    console.error(`No se encontró la atracción con ID: ${attractionId}`);
    return null;
  }

  return (
    <motion.div
      className="bg-white rounded-lg shadow-xl overflow-hidden group hover:shadow-premium"
      initial={{ opacity: 0, y: 30 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.6, delay: index * 0.1 }}
    >
      <div className="h-64 overflow-hidden relative">
        <ImageManager
          attractionId={attractionId}
          className="w-full h-full object-cover"
        />
        <div className="absolute top-0 left-0 bg-gradient-to-r from-black/50 to-transparent px-4 py-2 text-white">
          <span className="text-xs font-medium">{attraction.distance}</span>
        </div>
      </div>

      <div className="p-6">
        <h4 className="font-serif font-bold text-woodBrown-dark text-xl mb-2">{attraction.title}</h4>

        <p className="text-sm text-stoneGray-dark mb-4">
          {attraction.description}
        </p>

        <div className="flex justify-end">
          <a
            href="#"
            className="text-forestGreen hover:text-forestGreen-dark transition-colors flex items-center bg-forestGreen/10 hover:bg-forestGreen/20 px-3 py-1.5 rounded-full"
          >
            <span className="text-sm font-medium">Ver más</span>
            <span className="material-icons text-sm ml-1">arrow_forward</span>
          </a>
        </div>
      </div>
    </motion.div>
  );
};

export default AttractionCardEnhanced;
