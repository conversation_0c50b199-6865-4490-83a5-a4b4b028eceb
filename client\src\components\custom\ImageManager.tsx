import React, { useState, useEffect } from 'react';
import imageCatalog from '../../assets/image-catalog.json';
import FadeImage from './FadeImage';
import SimpleImage from './SimpleImage';
import { validateAttractionImages } from '../../utils/imageValidator';

interface ImageData {
  path: string;
  alt: string;
  width: number;
  height: number;
  format: string;
}

interface AttractionData {
  id: string;
  title: string;
  description: string;
  distance: string;
  images: ImageData[];
}

interface ImageManagerProps {
  attractionId: string;
  className?: string;
  singleImage?: boolean;
  imageIndex?: number;
}

/**
 * Componente para gestionar imágenes desde el catálogo centralizado
 *
 * @param attractionId - ID de la atracción en el catálogo
 * @param className - Clases CSS opcionales
 * @param singleImage - Si es true, muestra una sola imagen en lugar de una galería con efecto de desvanecimiento
 * @param imageIndex - Índice de la imagen a mostrar (solo si singleImage es true)
 */
const ImageManager: React.FC<ImageManagerProps> = ({
  attractionId,
  className = "w-full h-full object-cover",
  singleImage = false,
  imageIndex = 0
}) => {
  const [attraction, setAttraction] = useState<AttractionData | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [validationResults, setValidationResults] = useState<{
    valid: string[];
    invalid: string[];
    total: number;
  } | null>(null);

  useEffect(() => {
    // Buscar la atracción en el catálogo
    const foundAttraction = (imageCatalog.attractions as AttractionData[]).find(
      (attr) => attr.id === attractionId
    );

    if (foundAttraction) {
      setAttraction(foundAttraction);

      // Validar las imágenes de la atracción
      validateAttractionImages(attractionId)
        .then(results => {
          setValidationResults(results);

          // Mostrar advertencias en la consola para imágenes no válidas
          if (results.invalid.length > 0) {
            console.warn(`⚠️ Atracción "${foundAttraction.title}": ${results.invalid.length} de ${results.total} imágenes no se encontraron.`);
            results.invalid.forEach(path => {
              console.error(`🔴 Imagen no encontrada: ${path}`);
            });
          } else {
            console.info(`✅ Atracción "${foundAttraction.title}": Todas las imágenes (${results.total}) son válidas.`);
          }
        })
        .catch(err => {
          console.error(`Error al validar imágenes para ${attractionId}:`, err);
        });
    } else {
      setError(`No se encontró la atracción con ID: ${attractionId}`);
      console.error(`🔴 No se encontró la atracción con ID: ${attractionId}`);
    }

    setLoading(false);
  }, [attractionId]);

  // Mostrar mensaje de error si no se encuentra la atracción
  if (error) {
    return (
      <div className="error-container bg-red-100 p-4 rounded-lg">
        <p className="text-red-600">{error}</p>
      </div>
    );
  }

  // Mostrar indicador de carga mientras se busca la atracción
  if (loading || !attraction) {
    return <div className="loading-container">Cargando...</div>;
  }

  // Mostrar advertencia visual si hay imágenes inválidas
  const hasInvalidImages = validationResults && validationResults.invalid.length > 0;
  if (hasInvalidImages && process.env.NODE_ENV === 'development') {
    return (
      <div className="relative">
        {/* Contenido normal */}
        {singleImage ? (
          <div className="relative">
            <SimpleImage
              src={attraction.images[imageIndex]?.path || attraction.images[0].path}
              alt={attraction.images[imageIndex]?.alt || attraction.title}
              className={className}
            />
            <div className="absolute inset-0 bg-red-500 bg-opacity-30 flex items-center justify-center">
              <div className="bg-white p-3 rounded-lg shadow-lg text-center">
                <p className="text-red-600 font-bold">⚠️ Error de imagen</p>
                <p className="text-sm text-gray-700">Algunas imágenes no se encontraron</p>
                <p className="text-xs text-gray-500 mt-1">Solo visible en desarrollo</p>
              </div>
            </div>
          </div>
        ) : (
          <div className="relative">
            <FadeImage
              images={attraction.images.map(img => img.path)}
              alt={attraction.title}
              className={className}
            />
            <div className="absolute inset-0 bg-red-500 bg-opacity-30 flex items-center justify-center">
              <div className="bg-white p-3 rounded-lg shadow-lg text-center">
                <p className="text-red-600 font-bold">⚠️ Error de imagen</p>
                <p className="text-sm text-gray-700">Algunas imágenes no se encontraron</p>
                <p className="text-xs text-gray-500 mt-1">Solo visible en desarrollo</p>
              </div>
            </div>
          </div>
        )}
      </div>
    );
  }

  // Si se solicita una sola imagen
  if (singleImage) {
    const image = attraction.images[imageIndex] || attraction.images[0];

    return (
      <SimpleImage
        src={image.path}
        alt={image.alt}
        className={className}
      />
    );
  }

  // Si se solicitan múltiples imágenes con efecto de desvanecimiento
  const imagePaths = attraction.images.map(img => img.path);

  return (
    <FadeImage
      images={imagePaths}
      alt={attraction.title}
      className={className}
      interval={6000} // 6 segundos entre transiciones
      initialDelay={500} // 0.5 segundos de retraso inicial
    />
  );
};

export default ImageManager;
