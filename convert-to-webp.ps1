#!/usr/bin/env pwsh
<#
.SYNOPSIS
    Script para convertir imágenes JPG y PNG a formato WebP.

.DESCRIPTION
    Este script convierte imágenes JPG y PNG a formato WebP con buena calidad y tamaño reducido.
    Utiliza el módulo System.Drawing para realizar la conversión.

.NOTES
    Autor: Augment Agent
    Fecha: Mayo 2024
#>

# Función para mostrar mensajes con formato
function Write-ProcessMessage {
    param (
        [string]$Message,
        [string]$Type = "Info"
    )
    
    $color = switch ($Type) {
        "Info"    { "White" }
        "Success" { "Green" }
        "Warning" { "Yellow" }
        "Error"   { "Red" }
        default   { "White" }
    }
    
    Write-Host $Message -ForegroundColor $color
}

# Mostrar encabezado
Write-Host "=======================================================" -ForegroundColor Cyan
Write-Host "  CONVERSION DE IMAGENES A FORMATO WEBP" -ForegroundColor Cyan
Write-Host "=======================================================" -ForegroundColor Cyan

# Instalar el módulo WebP si no está instalado
if (-not (Get-Module -ListAvailable -Name WebP)) {
    Write-ColorMessage "Instalando el módulo WebP..." -ForegroundColor "Yellow"
    Install-Module -Name WebP -Force -Scope CurrentUser
}

# Importar el módulo WebP
Import-Module WebP

# Función para convertir una imagen a WebP
function Convert-ToWebP {
    param (
        [Parameter(Mandatory = $true)]
        [string]$InputPath,
        [Parameter(Mandatory = $true)]
        [string]$OutputPath,
        [Parameter(Mandatory = $false)]
        [int]$Quality = 85
    )
    try {
        # Verificar si el archivo de entrada existe
        if (-not (Test-Path $InputPath)) {
            Write-ProcessMessage "Error: El archivo de entrada no existe: $InputPath" -Type "Error"
            return $false
        }
        # Crear el directorio de salida si no existe
        $outputDir = Split-Path -Path $OutputPath -Parent
        if (-not (Test-Path $outputDir)) {
            New-Item -ItemType Directory -Path $outputDir -Force | Out-Null
        }
        # Convertir la imagen a WebP
        Write-ProcessMessage "Convirtiendo $InputPath a WebP..." -Type "Warning"
        ConvertTo-WebP -Path $InputPath -Out $OutputPath -Q $Quality
        # Verificar si la conversión fue exitosa
        if (Test-Path $OutputPath) {
            $inputSize = (Get-Item $InputPath).Length
            $outputSize = (Get-Item $OutputPath).Length
            $savingsPercent = [math]::Round(100 - ($outputSize / $inputSize * 100), 2)
            Write-ProcessMessage "Conversión exitosa: $OutputPath" -Type "Success"
            Write-ProcessMessage "Tamaño original: $([math]::Round($inputSize / 1KB, 2)) KB" -Type "Info"
            Write-ProcessMessage "Tamaño nuevo: $([math]::Round($outputSize / 1KB, 2)) KB" -Type "Info"
            Write-ProcessMessage "Ahorro: $savingsPercent%" -Type "Success"
            return $true
        } else {
            Write-ProcessMessage "Error: No se pudo crear el archivo WebP: $OutputPath" -Type "Error"
            return $false
        }
    } catch {
        Write-ProcessMessage "Error al convertir la imagen: $_" -Type "Error"
        return $false
    }
}

# Lista de imágenes a convertir
$imagesToConvert = @(
    @{
        InputPath = "client\src\assets\cabaña\piscina\piscina1.jpg"
        OutputPath = "client\src\assets\cabaña\piscina\piscina1.webp"
    },
    @{
        InputPath = "client\src\assets\cabaña\piscina\piscina2.jpg"
        OutputPath = "client\src\assets\cabaña\piscina\piscina2.webp"
    },
    @{
        InputPath = "client\src\assets\cabaña\piscina\piscina3.jpg"
        OutputPath = "client\src\assets\cabaña\piscina\piscina3.webp"
    },
    @{
        InputPath = "client\src\assets\cabaña\piscina\piscina4.jpg"
        OutputPath = "client\src\assets\cabaña\piscina\piscina4.webp"
    },
    @{
        InputPath = "client\src\assets\cabaña\piscina\piscina5.jpg"
        OutputPath = "client\src\assets\cabaña\piscina\piscina5.webp"
    },
    @{
        InputPath = "client\src\assets\cabaña\piezas\pieza1.jpg"
        OutputPath = "client\src\assets\cabaña\piezas\pieza1.webp"
    },
    @{
        InputPath = "client\src\assets\cabaña\piezas\pieza2.jpg"
        OutputPath = "client\src\assets\cabaña\piezas\pieza2.webp"
    },
    @{
        InputPath = "client\src\assets\cabaña\piezas\pieza3.jpg"
        OutputPath = "client\src\assets\cabaña\piezas\pieza3.webp"
    },
    @{
        InputPath = "client\src\assets\cabaña\baño.jpg"
        OutputPath = "client\src\assets\cabaña\baño.webp"
    },
    @{
        InputPath = "client\src\assets\cabaña\living.jpg"
        OutputPath = "client\src\assets\cabaña\living.webp"
    },
    @{
        InputPath = "client\src\assets\cabaña\cocina.jpg"
        OutputPath = "client\src\assets\cabaña\cocina.webp"
    },
    @{
        InputPath = "client\src\assets\cabaña\mesa-pool.jpg"
        OutputPath = "client\src\assets\cabaña\mesa-pool.webp"
    }
)

# Convertir las imágenes
$successCount = 0
$totalCount = $imagesToConvert.Count

foreach ($image in $imagesToConvert) {
    Write-ProcessMessage "Procesando: $($image.InputPath)" -Type "Info"
    $success = Convert-ToWebP -InputPath $image.InputPath -OutputPath $image.OutputPath -Quality 80
    
    if ($success) {
        $successCount++
    }
    
    Write-ProcessMessage "-------------------------------------------------------" -Type "Info"
}

# Mostrar resumen
Write-ProcessMessage "=======================================================" -Type "Info"
Write-ProcessMessage "  RESUMEN DE CONVERSION" -Type "Info"
Write-ProcessMessage "=======================================================" -Type "Info"
Write-ProcessMessage "Total de imagenes procesadas: $totalCount" -Type "Info"
Write-ProcessMessage "Conversiones exitosas: $successCount" -Type "Success"
Write-ProcessMessage "Conversiones fallidas: $($totalCount - $successCount)" -Type "Error"
Write-ProcessMessage "=======================================================" -Type "Info"
