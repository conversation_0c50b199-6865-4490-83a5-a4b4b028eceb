const sharp = require('sharp');
const fs = require('fs').promises;
const path = require('path');

const imagesToConvert = [
  {
    input: 'client/src/assets/cabana/bano.jpg',
    output: 'client/src/assets/cabana/bano.webp'
  },
  {
    input: 'client/src/assets/cabana/cocina.jpg',
    output: 'client/src/assets/cabana/cocina.webp'
  },
  {
    input: 'client/src/assets/cabana/living.jpg',
    output: 'client/src/assets/cabana/living.webp'
  },
  {
    input: 'client/src/assets/cabana/mesa-pool.jpg',
    output: 'client/src/assets/cabana/mesa-pool.webp'
  },
  {
    input: 'client/src/assets/cabana/rooms/pieza2.jpg',
    output: 'client/src/assets/cabana/rooms/pieza2.webp'
  },
  {
    input: 'client/src/assets/cabana/rooms/pieza4.jpg',
    output: 'client/src/assets/cabana/rooms/pieza4.webp'
  },
  {
    input: 'client/src/assets/landing/desierto.jpg',
    output: 'client/src/assets/landing/desierto.webp'
  }
];

async function convertImage(input, output) {
  try {
    await sharp(input)
      .webp({ quality: 85 })
      .toFile(output);
    console.log(`✅ Converted ${input} to ${output}`);
  } catch (error) {
    console.error(`❌ Error converting ${input}:`, error);
  }
}

async function ensureDirectoryExists(filePath) {
  const directory = path.dirname(filePath);
  try {
    await fs.access(directory);
  } catch {
    await fs.mkdir(directory, { recursive: true });
  }
}

async function main() {
  let success = 0;
  for (const { input, output } of imagesToConvert) {
    try {
      await fs.access(input);
      await ensureDirectoryExists(output);
      await convertImage(input, output);
      success++;
    } catch (error) {
      if (error.code === 'ENOENT') {
        console.error(`❌ File not found: ${input}`);
      } else {
        console.error(`❌ Error processing ${input}:`, error);
      }
    }
  }
  console.log('\nResumen:');
  console.log(`Total: ${imagesToConvert.length}, Exitosas: ${success}, Fallidas: ${imagesToConvert.length - success}`);
}

main().catch(console.error);
