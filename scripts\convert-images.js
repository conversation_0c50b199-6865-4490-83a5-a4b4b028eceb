/**
 * Script para convertir imágenes a formato WebP y optimizarlas
 *
 * Este script:
 * 1. Convierte imágenes JPG, JPEG, PNG y JFIF a formato WebP
 * 2. Optimiza imágenes WebP existentes
 * 3. Estandariza los nombres de archivo según las convenciones del proyecto
 */

import fs from 'fs';
import path from 'path';
import sharp from 'sharp';
import { fileURLToPath } from 'url';

// Obtener el directorio actual en módulos ES
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuración
const ASSETS_DIR = path.join(process.cwd(), 'client', 'src', 'assets', 'atracciones');
const WEBP_QUALITY = 80; // Calidad de las imágenes WebP (0-100)
const WEBP_OPTIMIZATION = { quality: WEBP_QUALITY };

// Mapeo de carpetas a nombres estandarizados para archivos
const FOLDER_TO_PREFIX = {
  'cocha-resbaladero': 'cocha-resbaladero',
  'iglesia-san-andres': 'iglesia-san-andres',
  'parque-dinosaurios': 'parque-dinosaurios',
  'pueblo-de-pica': 'pueblo-de-pica',
  'reserva-pampa-del-tamarugal': 'reserva-pampa-del-tamarugal',
  'salar-del-huasco': 'salar-del-huasco'
};

// Función para obtener todas las imágenes en las carpetas de atracciones
async function getImageFiles() {
  const imageFiles = [];

  // Leer todas las carpetas en el directorio de atracciones
  const folders = fs.readdirSync(ASSETS_DIR).filter(
    folder => fs.statSync(path.join(ASSETS_DIR, folder)).isDirectory()
  );

  // Para cada carpeta, obtener todos los archivos de imagen
  for (const folder of folders) {
    const folderPath = path.join(ASSETS_DIR, folder);
    const files = fs.readdirSync(folderPath).filter(file => {
      const ext = path.extname(file).toLowerCase();
      return ['.jpg', '.jpeg', '.png', '.webp', '.jfif'].includes(ext);
    });

    // Agregar información de cada archivo
    for (const file of files) {
      const filePath = path.join(folderPath, file);
      const stats = fs.statSync(filePath);

      imageFiles.push({
        folder,
        file,
        path: filePath,
        size: stats.size,
        extension: path.extname(file).toLowerCase(),
        isWebP: path.extname(file).toLowerCase() === '.webp'
      });
    }
  }

  return imageFiles;
}

// Función para generar un nombre de archivo estandarizado
function generateStandardFileName(folder, file, index) {
  const prefix = FOLDER_TO_PREFIX[folder];
  if (!prefix) {
    console.warn(`⚠️ No se encontró un prefijo para la carpeta: ${folder}`);
    return `${folder}-${index}.webp`;
  }

  return `${prefix}-${index}.webp`;
}

// Función para convertir una imagen a WebP
async function convertToWebP(imagePath, outputPath) {
  try {
    await sharp(imagePath)
      .webp(WEBP_OPTIMIZATION)
      .toFile(outputPath);

    const inputStats = fs.statSync(imagePath);
    const outputStats = fs.statSync(outputPath);
    const savingsPercent = ((inputStats.size - outputStats.size) / inputStats.size * 100).toFixed(2);

    console.log(`✅ Convertida: ${path.basename(imagePath)} → ${path.basename(outputPath)}`);
    console.log(`   Tamaño original: ${(inputStats.size / 1024).toFixed(2)} KB`);
    console.log(`   Nuevo tamaño: ${(outputStats.size / 1024).toFixed(2)} KB (${savingsPercent}% de ahorro)`);

    return {
      originalSize: inputStats.size,
      newSize: outputStats.size,
      savings: inputStats.size - outputStats.size,
      savingsPercent: parseFloat(savingsPercent)
    };
  } catch (error) {
    console.error(`❌ Error al convertir ${imagePath}:`, error);
    throw error;
  }
}

// Función para optimizar una imagen WebP existente
async function optimizeWebP(imagePath, outputPath) {
  try {
    await sharp(imagePath)
      .webp(WEBP_OPTIMIZATION)
      .toFile(outputPath);

    const inputStats = fs.statSync(imagePath);
    const outputStats = fs.statSync(outputPath);
    const savingsPercent = ((inputStats.size - outputStats.size) / inputStats.size * 100).toFixed(2);

    // Si la imagen optimizada es más grande, mantener la original
    if (outputStats.size >= inputStats.size) {
      fs.unlinkSync(outputPath); // Eliminar la versión optimizada
      console.log(`ℹ️ No se optimizó: ${path.basename(imagePath)} (la versión optimizada es más grande)`);
      return {
        originalSize: inputStats.size,
        newSize: inputStats.size,
        savings: 0,
        savingsPercent: 0
      };
    }

    console.log(`✅ Optimizada: ${path.basename(imagePath)} → ${path.basename(outputPath)}`);
    console.log(`   Tamaño original: ${(inputStats.size / 1024).toFixed(2)} KB`);
    console.log(`   Nuevo tamaño: ${(outputStats.size / 1024).toFixed(2)} KB (${savingsPercent}% de ahorro)`);

    return {
      originalSize: inputStats.size,
      newSize: outputStats.size,
      savings: inputStats.size - outputStats.size,
      savingsPercent: parseFloat(savingsPercent)
    };
  } catch (error) {
    console.error(`❌ Error al optimizar ${imagePath}:`, error);
    throw error;
  }
}

// Función principal
async function main() {
  console.log('🔍 Buscando imágenes en las carpetas de atracciones...');
  const imageFiles = await getImageFiles();

  console.log(`📊 Encontradas ${imageFiles.length} imágenes en total.`);
  console.log(`   - ${imageFiles.filter(img => img.isWebP).length} imágenes WebP`);
  console.log(`   - ${imageFiles.filter(img => !img.isWebP).length} imágenes no WebP\n`);

  // Estadísticas
  let totalOriginalSize = 0;
  let totalNewSize = 0;
  let convertedCount = 0;
  let optimizedCount = 0;
  let skippedCount = 0;

  // Procesar cada carpeta
  for (const folder of Object.keys(FOLDER_TO_PREFIX)) {
    const folderPath = path.join(ASSETS_DIR, folder);

    // Verificar si la carpeta existe
    if (!fs.existsSync(folderPath)) {
      console.warn(`⚠️ La carpeta ${folder} no existe. Omitiendo...`);
      continue;
    }

    console.log(`\n📁 Procesando carpeta: ${folder}`);

    // Obtener imágenes de esta carpeta
    const folderImages = imageFiles.filter(img => img.folder === folder);

    // Ordenar imágenes: primero las WebP, luego las no WebP
    folderImages.sort((a, b) => {
      if (a.isWebP && !b.isWebP) return -1;
      if (!a.isWebP && b.isWebP) return 1;
      return a.file.localeCompare(b.file);
    });

    // Procesar imágenes
    let index = 1;
    const processedFiles = new Set();

    for (const image of folderImages) {
      // Generar nombre estandarizado
      const standardFileName = generateStandardFileName(folder, image.file, index);
      const outputPath = path.join(folderPath, standardFileName);

      // Evitar procesar el mismo archivo de salida más de una vez
      if (processedFiles.has(standardFileName)) {
        console.log(`⚠️ Ya existe un archivo con el nombre ${standardFileName}. Omitiendo...`);
        skippedCount++;
        continue;
      }

      // Si el archivo de salida ya existe y es el mismo que el de entrada, omitirlo
      if (image.file === standardFileName) {
        console.log(`ℹ️ El archivo ${image.file} ya tiene un nombre estandarizado.`);

        // Si es WebP, intentar optimizarlo
        if (image.isWebP) {
          const tempPath = path.join(folderPath, `temp-${standardFileName}`);
          try {
            const result = await optimizeWebP(image.path, tempPath);

            // Si se optimizó correctamente, reemplazar el original
            if (result.savings > 0) {
              fs.renameSync(tempPath, image.path);
              totalOriginalSize += result.originalSize;
              totalNewSize += result.newSize;
              optimizedCount++;
            } else {
              skippedCount++;
            }
          } catch (error) {
            // Si hay un error, eliminar el archivo temporal si existe
            if (fs.existsSync(tempPath)) {
              fs.unlinkSync(tempPath);
            }
            skippedCount++;
          }
        } else {
          skippedCount++;
        }

        continue;
      }

      // Procesar la imagen
      try {
        let result;

        if (image.isWebP) {
          // Optimizar imagen WebP
          result = await optimizeWebP(image.path, outputPath);
          optimizedCount++;
        } else {
          // Convertir a WebP
          result = await convertToWebP(image.path, outputPath);
          convertedCount++;
        }

        totalOriginalSize += result.originalSize;
        totalNewSize += result.newSize;
        processedFiles.add(standardFileName);
        index++;
      } catch (error) {
        console.error(`❌ Error al procesar ${image.file}:`, error);
      }
    }
  }

  // Mostrar estadísticas finales
  console.log('\n📊 Resumen:');
  console.log(`   - Imágenes convertidas: ${convertedCount}`);
  console.log(`   - Imágenes optimizadas: ${optimizedCount}`);
  console.log(`   - Imágenes omitidas: ${skippedCount}`);
  console.log(`   - Tamaño original total: ${(totalOriginalSize / 1024 / 1024).toFixed(2)} MB`);
  console.log(`   - Nuevo tamaño total: ${(totalNewSize / 1024 / 1024).toFixed(2)} MB`);
  console.log(`   - Ahorro total: ${((totalOriginalSize - totalNewSize) / 1024 / 1024).toFixed(2)} MB (${((totalOriginalSize - totalNewSize) / totalOriginalSize * 100).toFixed(2)}%)`);
}

// Ejecutar el script
main().catch(error => {
  console.error('❌ Error en el script:', error);
  process.exit(1);
});
