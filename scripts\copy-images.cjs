// Script para copiar imágenes a la carpeta pública
const fs = require('fs');
const path = require('path');

// Directorios de origen y destino
const sourceDirs = [
  {
    src: path.join(process.cwd(), 'client/src/assets/cabaña'),
    dest: path.join(process.cwd(), 'client/public/images/gallery/cabana')
  },
  {
    src: path.join(process.cwd(), 'client/src/assets/atracciones'),
    dest: path.join(process.cwd(), 'client/public/images/gallery/atracciones')
  }
];

// Asegurarse de que los directorios de destino existan
sourceDirs.forEach(dir => {
  if (!fs.existsSync(dir.dest)) {
    fs.mkdirSync(dir.dest, { recursive: true });
    console.log(`Directorio creado: ${dir.dest}`);
  }
});

// Función para copiar un archivo
function copyFile(source, destination) {
  try {
    fs.copyFileSync(source, destination);
    console.log(`Archivo copiado: ${path.basename(destination)}`);
    return true;
  } catch (error) {
    console.error(`Error al copiar ${source}:`, error);
    return false;
  }
}

// Función para procesar todas las imágenes en un directorio
function processDirectory(dir, destDir) {
  try {
    const files = fs.readdirSync(dir);

    for (const file of files) {
      const sourcePath = path.join(dir, file);
      const stat = fs.statSync(sourcePath);

      if (stat.isDirectory()) {
        // Si es un directorio, crear el directorio correspondiente en destino
        const nestedDestDir = path.join(destDir, file);
        if (!fs.existsSync(nestedDestDir)) {
          fs.mkdirSync(nestedDestDir, { recursive: true });
          console.log(`Subdirectorio creado: ${nestedDestDir}`);
        }
        // Procesar el subdirectorio recursivamente
        processDirectory(sourcePath, nestedDestDir);
      } else if (stat.isFile() && /\.(jpg|jpeg|png|gif|webp)$/i.test(file)) {
        // Si es una imagen, copiarla
        const destPath = path.join(destDir, file);
        copyFile(sourcePath, destPath);
      }
    }
  } catch (error) {
    console.error(`Error al procesar el directorio ${dir}:`, error);
  }
}

// Ejecutar el proceso de copia
function run() {
  console.log('Iniciando copia de imágenes...');
  
  // Procesar cada directorio de origen
  for (const dir of sourceDirs) {
    console.log(`Procesando directorio: ${dir.src}`);
    processDirectory(dir.src, dir.dest);
  }
  
  console.log('Copia completada.');
}

run();
