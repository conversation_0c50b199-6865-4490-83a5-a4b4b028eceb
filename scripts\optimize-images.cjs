// Script para optimizar imágenes para la web
const fs = require('fs');
const path = require('path');
const sharp = require('sharp');

// Función para manejar errores
function handleError(error) {
  console.error('Error:', error);
  process.exit(1);
}

// Directorios de origen y destino
const sourceDirs = [
  {
    src: path.join(process.cwd(), 'client/src/assets/cabana'),
    dest: path.join(process.cwd(), 'client/public/images/gallery/cabana')
  },
  {
    src: path.join(process.cwd(), 'client/src/assets/atracciones'),
    dest: path.join(process.cwd(), 'client/public/images/gallery/atracciones')
  }
];

// Asegurarse de que los directorios de destino existan
sourceDirs.forEach(dir => {
  if (!fs.existsSync(dir.dest)) {
    fs.mkdirSync(dir.dest, { recursive: true });
    console.log(`Directorio creado: ${dir.dest}`);
  }
});

// Configuración de optimización
const config = {
  width: 800,      // Ancho máximo
  quality: 80,     // Calidad de compresión (0-100)
  format: 'webp'   // Formato moderno con buena compresión
};

// Función para optimizar una imagen
async function optimizeImage(sourcePath, destPath, options = {}) {
  try {
    const image = sharp(sourcePath);
    const metadata = await image.metadata();

    // Mantener la relación de aspecto
    const width = options.width || metadata.width;

    await image
      .resize({ width, withoutEnlargement: true })
      .webp({ quality: options.quality || 80 })
      .toFile(destPath);

    console.log(`Imagen optimizada: ${path.basename(destPath)}`);
    return { success: true, path: destPath };
  } catch (error) {
    console.error(`Error al optimizar ${sourcePath}:`, error);
    return { success: false, error };
  }
}

// Función para procesar todas las imágenes en un directorio
async function processDirectory(dir, destDir, options = {}) {
  const files = fs.readdirSync(dir);

  for (const file of files) {
    const sourcePath = path.join(dir, file);
    const stat = fs.statSync(sourcePath);

    if (stat.isDirectory()) {
      // Si es un directorio, crear el directorio correspondiente en destino
      const nestedDestDir = path.join(destDir, file);
      if (!fs.existsSync(nestedDestDir)) {
        fs.mkdirSync(nestedDestDir, { recursive: true });
      }
      // Procesar el subdirectorio recursivamente
      await processDirectory(sourcePath, nestedDestDir, options);
    } else if (stat.isFile() && /\.(jpg|jpeg|png|gif)$/i.test(file)) {
      // Si es una imagen, optimizarla
      const destPath = path.join(destDir, `${path.parse(file).name}.${options.format || 'webp'}`);
      await optimizeImage(sourcePath, destPath, options);
    }
  }
}

// Ejecutar el proceso de optimización
async function run() {
  console.log('Iniciando optimización de imágenes...');

  // Procesar cada directorio de origen
  for (const dir of sourceDirs) {
    console.log(`Procesando directorio: ${dir.src}`);
    await processDirectory(dir.src, dir.dest, config);
  }

  console.log('Optimización completada.');
}

run().catch(handleError);
