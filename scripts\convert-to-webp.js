// Cambiar a CommonJS para compatibilidad con require
// Renombrar este archivo a .cjs o usar import en vez de require si se mantiene .js
// Solución rápida: copiar este contenido a convert-to-webp.cjs y ejecutar con node
// Si se mantiene .js, usar import sharp from 'sharp'; y fs/promises

const sharp = require('sharp');
const fs = require('fs').promises;
const path = require('path');

const imagesToConvert = [
  {
    input: 'client/src/assets/cabaña/piscina/piscina1.jpg',
    output: 'client/src/assets/cabaña/piscina/piscina1.webp'
  },
  {
    input: 'client/src/assets/cabaña/piscina/piscina2.jpg',
    output: 'client/src/assets/cabaña/piscina/piscina2.webp'
  },
  {
    input: 'client/src/assets/cabaña/piscina/piscina3.jpg',
    output: 'client/src/assets/cabaña/piscina/piscina3.webp'
  },
  {
    input: 'client/src/assets/cabaña/piscina/piscina4.jpg',
    output: 'client/src/assets/cabaña/piscina/piscina4.webp'
  },
  {
    input: 'client/src/assets/cabaña/piscina/piscina5.jpg',
    output: 'client/src/assets/cabaña/piscina/piscina5.webp'
  },
  {
    input: 'client/src/assets/cabaña/piezas/pieza1.jpg',
    output: 'client/src/assets/cabaña/piezas/pieza1.webp'
  },
  {
    input: 'client/src/assets/cabaña/piezas/pieza2.jpg',
    output: 'client/src/assets/cabaña/piezas/pieza2.webp'
  },
  {
    input: 'client/src/assets/cabaña/piezas/pieza3.jpg',
    output: 'client/src/assets/cabaña/piezas/pieza3.webp'
  },
  {
    input: 'client/src/assets/cabaña/baño.jpg',
    output: 'client/src/assets/cabaña/baño.webp'
  },
  {
    input: 'client/src/assets/cabaña/living.jpg',
    output: 'client/src/assets/cabaña/living.webp'
  },
  {
    input: 'client/src/assets/cabaña/cocina.jpg',
    output: 'client/src/assets/cabaña/cocina.webp'
  },
  {
    input: 'client/src/assets/cabaña/mesa-pool.jpg',
    output: 'client/src/assets/cabaña/mesa-pool.webp'
  }
];

async function convertImage(input, output) {
  try {
    // Asegurarnos de que el directorio de salida existe
    await fs.mkdir(path.dirname(output), { recursive: true });

    // Convertir la imagen a WebP
    await sharp(input)
      .webp({ quality: 85 })
      .toFile(output);

    const inputStats = await fs.stat(input);
    const outputStats = await fs.stat(output);
    const savings = ((inputStats.size - outputStats.size) / inputStats.size * 100).toFixed(2);

    console.log(`✅ Convertida: ${input}`);
    console.log(`   Tamaño original: ${(inputStats.size / 1024).toFixed(2)} KB`);
    console.log(`   Tamaño nuevo: ${(outputStats.size / 1024).toFixed(2)} KB`);
    console.log(`   Ahorro: ${savings}%\n`);

    return true;
  } catch (error) {
    console.error(`❌ Error al convertir ${input}:`, error.message);
    return false;
  }
}

async function main() {
  console.log('🔄 Iniciando conversión de imágenes a WebP...\n');
  
  let successful = 0;
  const total = imagesToConvert.length;

  for (const image of imagesToConvert) {
    if (await convertImage(image.input, image.output)) {
      successful++;
    }
  }

  console.log('📊 Resumen:');
  console.log(`   Total de imágenes: ${total}`);
  console.log(`   Conversiones exitosas: ${successful}`);
  console.log(`   Conversiones fallidas: ${total - successful}`);
}

main().catch(console.error);
