import imagemin from 'imagemin';
import imageminWebp from 'imagemin-webp';
import fs from 'fs';
import path from 'path';

// Función para convertir una imagen a WebP
async function convertToWebP(inputPath, outputPath, quality = 80) {
  try {
    const files = await imagemin([inputPath], {
      destination: path.dirname(outputPath),
      plugins: [
        imageminWebp({
          quality: quality,
        }),
      ],
    });

    // Renombrar el archivo si es necesario
    if (files.length > 0) {
      const generatedPath = files[0].destinationPath;
      const desiredPath = outputPath;

      if (generatedPath !== desiredPath) {
        fs.renameSync(generatedPath, desiredPath);
      }

      console.log(`Converted: ${inputPath} -> ${outputPath}`);

      // Mostrar estadísticas de tamaño
      const inputSize = fs.statSync(inputPath).size;
      const outputSize = fs.statSync(outputPath).size;
      const savingsPercent = ((1 - (outputSize / inputPath)) * 100).toFixed(2);

      console.log(`Original size: ${(inputSize / 1024).toFixed(2)} KB`);
      console.log(`New size: ${(outputSize / 1024).toFixed(2)} KB`);
      console.log(`Savings: ${savingsPercent}%`);

      return true;
    } else {
      console.error(`Failed to convert: ${inputPath}`);
      return false;
    }
  } catch (error) {
    console.error(`Error converting ${inputPath}: ${error.message}`);
    return false;
  }
}

// Lista de imágenes a convertir
const imagesToConvert = [
  {
    inputPath: 'client/src/assets/atracciones/parque-dinosaurios/Parque_dinosaurios1.jpg',
    outputPath: 'client/src/assets/atracciones/parque-dinosaurios/Parque_dinosaurios1.webp',
  },
  {
    inputPath: 'client/src/assets/atracciones/parque-dinosaurios/Parque_dinosaurios2.jpg',
    outputPath: 'client/src/assets/atracciones/parque-dinosaurios/Parque_dinosaurios2.webp',
  },
  {
    inputPath: 'client/src/assets/atracciones/parque-dinosaurios/Parque_dinosaurios3.jpg',
    outputPath: 'client/src/assets/atracciones/parque-dinosaurios/Parque_dinosaurios3.webp',
  },
  {
    inputPath: 'client/src/assets/atracciones/Pueblo-de-pica/pica.jpg',
    outputPath: 'client/src/assets/atracciones/Pueblo-de-pica/pica.webp',
  },
  {
    inputPath: 'client/src/assets/atracciones/Pueblo-de-pica/pica2.jpg',
    outputPath: 'client/src/assets/atracciones/Pueblo-de-pica/pica2.webp',
  },
  {
    inputPath: 'client/src/assets/atracciones/Pueblo-de-pica/plaza.jpg',
    outputPath: 'client/src/assets/atracciones/Pueblo-de-pica/plaza.webp',
  },
];

// Convertir todas las imágenes
async function convertAllImages() {
  console.log('=== Starting image conversion to WebP ===');

  let successCount = 0;

  for (const image of imagesToConvert) {
    console.log(`\nProcessing: ${image.inputPath}`);
    const success = await convertToWebP(image.inputPath, image.outputPath, 80);

    if (success) {
      successCount++;
    }

    console.log('-------------------------------------------');
  }

  console.log('\n=== Conversion Summary ===');
  console.log(`Total images processed: ${imagesToConvert.length}`);
  console.log(`Successful conversions: ${successCount}`);
  console.log(`Failed conversions: ${imagesToConvert.length - successCount}`);
  console.log('===========================');
}

// Ejecutar la conversión
convertAllImages().catch(error => {
  console.error('Error during conversion process:', error);
});
