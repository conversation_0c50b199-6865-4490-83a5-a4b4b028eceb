/**
 * Script de Prueba para Conversión de Imágenes WebP
 * 
 * Este script realiza una prueba del sistema de conversión sin modificar archivos existentes.
 * Útil para verificar que el sistema funciona correctamente antes de ejecutar el script principal.
 */

import fs from 'fs';
import path from 'path';
import sharp from 'sharp';
import { fileURLToPath } from 'url';

// Obtener el directorio actual en módulos ES
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuración
const ASSETS_DIR = path.join(process.cwd(), 'client', 'src', 'assets', 'atracciones');
const CATALOG_PATH = path.join(process.cwd(), 'client', 'src', 'assets', 'image-catalog.json');

/**
 * Función de prueba para validar el sistema
 */
async function testImageConversion() {
  console.log('🧪 Iniciando Prueba del Sistema de Conversión de Imágenes');
  console.log('========================================================\n');

  // 1. Verificar directorios
  console.log('🔍 Verificando directorios...');
  
  if (!fs.existsSync(ASSETS_DIR)) {
    console.error(`❌ Error: El directorio de assets no existe: ${ASSETS_DIR}`);
    return false;
  }
  console.log(`✅ Directorio de assets encontrado: ${ASSETS_DIR}`);

  if (!fs.existsSync(CATALOG_PATH)) {
    console.warn(`⚠️ Advertencia: El catálogo no existe: ${CATALOG_PATH}`);
  } else {
    console.log(`✅ Catálogo encontrado: ${CATALOG_PATH}`);
  }

  // 2. Verificar dependencias
  console.log('\n🔍 Verificando dependencias...');
  try {
    const sharpInfo = sharp();
    console.log('✅ Sharp está disponible y funcionando');
  } catch (error) {
    console.error('❌ Error: Sharp no está disponible:', error.message);
    return false;
  }

  // 3. Escanear imágenes
  console.log('\n🔍 Escaneando imágenes...');
  let totalImages = 0;
  let webpImages = 0;
  let otherImages = 0;

  try {
    const folders = fs.readdirSync(ASSETS_DIR).filter(
      folder => fs.statSync(path.join(ASSETS_DIR, folder)).isDirectory()
    );

    console.log(`📁 Carpetas encontradas: ${folders.length}`);
    
    for (const folder of folders) {
      const folderPath = path.join(ASSETS_DIR, folder);
      const files = fs.readdirSync(folderPath).filter(file => {
        const ext = path.extname(file).toLowerCase();
        return ['.jpg', '.jpeg', '.png', '.webp', '.jfif'].includes(ext);
      });

      console.log(`   📂 ${folder}: ${files.length} imágenes`);
      
      files.forEach(file => {
        totalImages++;
        if (path.extname(file).toLowerCase() === '.webp') {
          webpImages++;
        } else {
          otherImages++;
        }
      });
    }

    console.log(`\n📊 Resumen de imágenes:`);
    console.log(`   Total: ${totalImages}`);
    console.log(`   WebP: ${webpImages}`);
    console.log(`   Otros formatos: ${otherImages}`);

  } catch (error) {
    console.error('❌ Error al escanear imágenes:', error.message);
    return false;
  }

  // 4. Verificar catálogo
  console.log('\n🔍 Verificando catálogo...');
  try {
    if (fs.existsSync(CATALOG_PATH)) {
      const catalogContent = fs.readFileSync(CATALOG_PATH, 'utf8');
      const catalog = JSON.parse(catalogContent);
      
      if (catalog.attractions && Array.isArray(catalog.attractions)) {
        console.log(`✅ Catálogo válido con ${catalog.attractions.length} atracciones`);
        
        // Verificar estructura de cada atracción
        let validAttractions = 0;
        catalog.attractions.forEach(attraction => {
          if (attraction.id && attraction.title && attraction.images && Array.isArray(attraction.images)) {
            validAttractions++;
          }
        });
        
        console.log(`✅ Atracciones válidas: ${validAttractions}/${catalog.attractions.length}`);
      } else {
        console.warn('⚠️ El catálogo no tiene la estructura esperada');
      }
    }
  } catch (error) {
    console.error('❌ Error al verificar catálogo:', error.message);
  }

  // 5. Prueba de conversión (sin guardar)
  console.log('\n🧪 Realizando prueba de conversión...');
  try {
    // Crear una imagen de prueba pequeña en memoria
    const testBuffer = await sharp({
      create: {
        width: 100,
        height: 100,
        channels: 3,
        background: { r: 255, g: 0, b: 0 }
      }
    })
    .png()
    .toBuffer();

    // Convertir a WebP
    const webpBuffer = await sharp(testBuffer)
      .webp({ quality: 80 })
      .toBuffer();

    console.log(`✅ Prueba de conversión exitosa`);
    console.log(`   PNG de prueba: ${testBuffer.length} bytes`);
    console.log(`   WebP resultante: ${webpBuffer.length} bytes`);
    console.log(`   Ahorro: ${((testBuffer.length - webpBuffer.length) / testBuffer.length * 100).toFixed(2)}%`);

  } catch (error) {
    console.error('❌ Error en prueba de conversión:', error.message);
    return false;
  }

  // 6. Verificar permisos de escritura
  console.log('\n🔍 Verificando permisos de escritura...');
  try {
    const testFile = path.join(ASSETS_DIR, 'test-permissions.tmp');
    fs.writeFileSync(testFile, 'test');
    fs.unlinkSync(testFile);
    console.log('✅ Permisos de escritura verificados');
  } catch (error) {
    console.error('❌ Error de permisos de escritura:', error.message);
    console.error('💡 Sugerencia: Ejecutar como administrador o verificar permisos de carpeta');
    return false;
  }

  console.log('\n🎉 Todas las pruebas completadas exitosamente!');
  console.log('✅ El sistema está listo para procesar imágenes');
  console.log('\n💡 Para ejecutar la conversión real, usar: node scripts/convert-images.js');
  
  return true;
}

// Ejecutar pruebas
testImageConversion().catch(error => {
  console.error('\n❌ Error en las pruebas:', error.message);
  process.exit(1);
});
